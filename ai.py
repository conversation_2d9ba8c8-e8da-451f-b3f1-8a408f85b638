import json
import base64
import pyautogui
from PIL import ImageGrab
import time
import math
import random
from anthropic import Anthropic




class OptimizedCaptchaSolver:
    def __init__(self):
        # Anthropic API configuration
        self.api_key = "************************************************************************************************************"
        self.api_url = "https://api.anthropic.com/v1"

        # Initialize Anthropic client
        self.client = Anthropic(api_key=self.api_key)
        self.screen = None
        
        _,height = self._get_screen_res()

        # Captcha area coordinates (configurable for different screen resolutions)
        #For 1080p Desktop Demo Website
        #self.captcha_bbox = (1111, 611, 1525, 934)
        #For 1080p Deksop Demo Website
        #self.slide_button_absolute = (1159, 891)  # Precise slide button coordinates
        #self.captcha_bbox = (586, 414, 1001, 743)
        print(self.screen)
        if self.screen == "custom":
            if height == 1440:
                #2k res
                self.captcha_bbox = (492, 381, 826, 645)  # (left, top, right, bottom)
                self.slide_button_absolute = (534, 606) 
            else:
                self.captcha_bbox = (620, 481, 1032, 808)
                self.slide_button_absolute = (671, 757)

        else:
            if height == 1440:
                #2k res
                self.captcha_bbox = (492, 381, 826, 645)  # (left, top, right, bottom)
                self.slide_button_absolute = (534, 606) 
            else:
                self.captcha_bbox = (620, 481, 1032, 808)
                self.slide_button_absolute = (671, 757)
            #For 1080p Gmx Website
            #self.slide_button_absolute = (630, 695)  # Precise slide button coordinates

        
        
        # PERFORMANCE OPTIMIZATION SETTINGS
        self.complete_overlap_threshold = 5  # Maximum pixels for complete overlap
        self.close_object_threshold = 50     # Objects ≤50px apart use single drag
        self.api_timeout = 30                # API timeout in seconds

        # HUMAN-LIKE MOVEMENT SETTINGS
        self.use_human_movement = True       # Enable human-like movement patterns
        self.movement_randomness = 0.1       # Random timing variation (0.05-0.15 seconds)

        # RESOLUTION SCALING SETTINGS
        self.resolution_scale_factor = 1.0   # Adjust if screenshot resolution differs from expected
        self.auto_detect_scaling = True      # Automatically detect and apply scaling



    def _get_screen_res(self):
         #create a function that get the exact screen resolution of the user's screen and return it
        self.screen_width, self.screen_height = pyautogui.size()
        return self.screen_width, self.screen_height



    def geometric_progression_steps(self, distance, threshold=1e-12):
        """Calculate movement steps using geometric progression for human-like movement"""
        if distance <= 0:
            return [distance]  # If no movement needed, return single step

        # Calculate steps that start fast and gradually slow down
        steps = max(3, math.ceil((math.log(threshold) - math.log(abs(distance))) / math.log(0.6)))
        current_value = abs(distance)
        values_per_step = []

        for _ in range(min(steps, 15)):  # Limit to max 15 steps for performance
            current_value *= 0.6  # Each step is 60% of the previous
            values_per_step.append(current_value)

        # Ensure the total distance is covered
        total_covered = sum(values_per_step)
        if total_covered > 0:
            # Scale values to match exact distance
            scale_factor = abs(distance) / total_covered
            values_per_step = [v * scale_factor for v in values_per_step]

        # Apply direction (positive or negative)
        direction = 1 if distance >= 0 else -1
        return [v * direction for v in values_per_step]

    def human_like_drag(self, start_x, start_y, end_x, end_y, duration_base=2.0):
        """Execute human-like drag movement with variable speed and micro-pauses"""
        if not self.use_human_movement:
            # Fallback to original movement
            pyautogui.moveTo(start_x, start_y, duration=0.3)
            pyautogui.mouseDown(button='left')
            pyautogui.moveTo(end_x, end_y, duration=duration_base)
            pyautogui.mouseUp(button='left')
            return

        # Calculate movement steps
        distance = end_x - start_x
        movement_steps = self.geometric_progression_steps(distance)

        print(f"🎯 Human-like movement: {len(movement_steps)} steps over {distance:.1f}px")

        # Move to start position with slight randomness
        start_offset_x = random.uniform(-2, 2)
        start_offset_y = random.uniform(-1, 1)
        pyautogui.moveTo(start_x + start_offset_x, start_y + start_offset_y, duration=0.3)
        time.sleep(random.uniform(0.1, 0.2))

        # Start drag
        pyautogui.mouseDown(button='left')
        time.sleep(random.uniform(0.05, 0.1))

        # Execute movement steps
        current_x = start_x
        for i, step in enumerate(movement_steps):
            current_x += step

            # Add slight vertical variation for more human-like movement
            y_variation = random.uniform(-0.5, 0.5) if i % 3 == 0 else 0

            pyautogui.moveTo(current_x, start_y + y_variation, duration=0.02)

            # Variable timing between steps (faster at start, slower at end)
            base_delay = 0.02 + (i / len(movement_steps)) * 0.08  # 0.02 to 0.1 seconds
            random_delay = random.uniform(0, self.movement_randomness)
            time.sleep(base_delay + random_delay)

        # Final position adjustment
        pyautogui.moveTo(end_x, end_y, duration=0.1)
        time.sleep(random.uniform(0.05, 0.15))

        # Release mouse
        pyautogui.mouseUp(button='left')

    def add_human_pause(self, min_duration=0.1, max_duration=0.5):
        """Add a human-like pause with optional micro-movements"""
        pause_duration = random.uniform(min_duration, max_duration)

        # Occasionally add tiny mouse movements during pause (like humans do)
        if random.random() < 0.3:  # 30% chance of micro-movement
            current_pos = pyautogui.position()
            micro_x = random.uniform(-1, 1)
            micro_y = random.uniform(-1, 1)
            pyautogui.moveTo(current_pos.x + micro_x, current_pos.y + micro_y, duration=0.1)
            time.sleep(pause_duration - 0.1)
        else:
            time.sleep(pause_duration)

    def detect_resolution_scaling(self, image_path):
        """Detect resolution scaling factor based on image dimensions"""
        try:
            from PIL import Image
            with Image.open(image_path) as img:
                width, height = img.size

                # Common captcha dimensions and their expected scaling factors
                # These values may need adjustment based on your specific captcha type
                expected_width = 412  # Common captcha width
                expected_height = 327  # Common captcha height

                # Calculate scaling factors
                width_scale = width / expected_width
                height_scale = height / expected_height

                # Use the average of width and height scaling
                detected_scale = (width_scale + height_scale) / 2

                print(f"🔍 Image dimensions: {width}x{height}")
                print(f"🔍 Expected dimensions: {expected_width}x{expected_height}")
                print(f"🔍 Detected scale factor: {detected_scale:.3f}")

                return detected_scale

        except Exception as e:
            print(f"⚠️ Could not detect scaling: {e}")
            return 1.0  # Default to no scaling

    def apply_coordinate_scaling(self, coordinates, scale_factor=None):
        """Apply resolution scaling to coordinates"""
        if scale_factor is None:
            scale_factor = self.resolution_scale_factor

        if scale_factor == 1.0:
            return coordinates

        scaled_coords = {
            'x': coordinates['x'] * scale_factor,
            'y': coordinates['y'] * scale_factor
        }

        print(f"🔧 Scaling coordinates: ({coordinates['x']}, {coordinates['y']}) → ({scaled_coords['x']:.1f}, {scaled_coords['y']:.1f})")
        return scaled_coords

    def set_manual_scaling(self, scale_factor):
        """Manually set the resolution scaling factor"""
        self.resolution_scale_factor = scale_factor
        self.auto_detect_scaling = False  # Disable auto-detection when manually set
        print(f"🔧 Manual scaling factor set to: {scale_factor:.3f}")

    def reset_scaling(self):
        """Reset to automatic scaling detection"""
        self.resolution_scale_factor = 1.0
        self.auto_detect_scaling = True
        print("🔧 Reset to automatic scaling detection")

    def capture_screen_area(self, filename="captcha_temp.png"):
        """Optimized screenshot capture with minimal processing"""
        screenshot = ImageGrab.grab(bbox=self.captcha_bbox)
        screenshot.save(filename)
        return filename



    def analyze_with_vision(self, image_path):
        """Enhanced vision analysis with focus on complete overlap detection and adaptive strategy"""
        with open(image_path, "rb") as image_file:
            base64_image = base64.b64encode(image_file.read()).decode('utf-8')

        try:
            # Use Anthropic SDK for API call
            response = self.client.messages.create(
                model="claude-3-5-sonnet-20241022",  # Updated to latest Claude 3.5 Sonnet
                max_tokens=1024,
                temperature=0.05,  # Lower temperature for more consistent results
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": """Analyze this slider with EXTREME PRECISION for object detection and adaptive drag strategy.

CRITICAL OBJECT DETECTION REQUIREMENTS:
- Identify exactly TWO main objects in the slider
- Calculate the precise CENTER coordinates of each object (pixel-perfect accuracy required)
- Measure the exact distance between object centers
- Determine object sizes for complete overlap calculation
- Objects must be detected with sub-pixel precision

DISTANCE AND STRATEGY ANALYSIS:
- Calculate exact pixel distance between object centers
- If distance ≤50px: recommend SINGLE_DRAG (continuous drag without stops)
- If distance >50px: recommend TWO_STEP_DRAG (60% then complete)
- CRITICAL: Complete overlap means source object center aligns with target center within 5 pixels

IMPORTANT: Use EXACTLY "source_object" and "target_object" as names. Respond with ONLY the JSON object below, no additional text, explanations, or formatting:

{
  "objects": [
    {"name": "source_object", "coordinates": {"x": precise_x, "y": precise_y}, "size": {"width": w, "height": h}},
    {"name": "target_object", "coordinates": {"x": precise_x, "y": precise_y}, "size": {"width": w, "height": h}}
  ],
  "distance_analysis": {
    "pixel_distance": exact_distance_number,
    "drag_strategy": "SINGLE_DRAG|TWO_STEP_DRAG",
    "proximity_level": "CLOSE|FAR"
  },
  "move": {"from": "source_object", "to": "target_object"}
}"""
                            },
                            {
                                "type": "image",
                                "source": {
                                    "type": "base64",
                                    "media_type": "image/png",
                                    "data": base64_image
                                }
                            }
                        ]
                    }
                ]
            )

            # Convert Anthropic response to match expected format
            content = response.content[0].text
            print(f"🔍 Raw API Response: {content}")  # Debug output

            response_json = {
                "choices": [
                    {
                        "message": {
                            "content": content
                        }
                    }
                ]
            }

            return response_json

        except Exception as e:
            print(f"❌ Vision analysis error: {e}")
            return None



    def verify_complete_overlap(self, image_path, source_obj, target_obj):
        """Verify complete overlap using strict criteria - ACCURACY PRIORITY"""
        with open(image_path, "rb") as image_file:
            base64_image = base64.b64encode(image_file.read()).decode('utf-8')

        try:
            # Use Anthropic SDK for API call
            response = self.client.messages.create(
                model="claude-3-5-sonnet-20241022",  # Updated to latest Claude 3.5 Sonnet
                max_tokens=1024,
                temperature=0.01,  # Maximum precision
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": f"""CRITICAL COMPLETE OVERLAP VERIFICATION for {source_obj} and {target_obj}.

STRICT COMPLETE OVERLAP CRITERIA (NO PARTIAL OVERLAP ACCEPTED):
- The {source_obj} center must align with {target_obj} center within 5 pixels maximum
- The {source_obj} must visually cover/hide the majority of the {target_obj}
- NO GAPS visible between the objects
- NO PARTIAL OVERLAP - only complete overlap is acceptable
- The {source_obj} should appear to be positioned directly on top of the {target_obj}

VERIFICATION REQUIREMENTS:
- Measure exact center-to-center distance between objects
- Check if {source_obj} completely covers {target_obj}
- Verify no visible gaps or misalignment
- REJECT any partial overlap or edge-touching scenarios

IMPORTANT: Respond with ONLY the JSON object below, no additional text, explanations, or formatting:

{{
  "complete_overlap_achieved": true_or_false,
  "center_distance_pixels": exact_number,
  "overlap_quality": "COMPLETE|PARTIAL|NONE",
  "action_required": "STOP_SUCCESS|CONTINUE_DRAGGING",
  "alignment_description": "detailed description of current alignment"
}}"""
                            },
                            {
                                "type": "image",
                                "source": {
                                    "type": "base64",
                                    "media_type": "image/png",
                                    "data": base64_image
                                }
                            }
                        ]
                    }
                ]
            )

            content = response.content[0].text

            # Parse JSON response
            if "```json" in content:
                json_str = content.split("```json")[1].split("```")[0].strip()
            else:
                json_str = content.strip()

            return json.loads(json_str)

        except Exception as e:
            print(f"❌ Complete overlap verification error: {e}")
            return None



    def execute_movement(self, analysis_result):
        """OPTIMIZED movement execution with adaptive drag strategy"""
        try:
            # Parse AI response
            content = analysis_result["choices"][0]["message"]["content"]
            print(f"🔍 Content to parse: {content[:200]}...")  # Debug output

            # Handle JSON parsing
            if "```json" in content:
                json_str = content.split("```json")[1].split("```")[0].strip()
            else:
                json_str = content.strip()

            print(f"🔍 JSON string: {json_str[:200]}...")  # Debug output

            # Clean up common JSON formatting issues
            json_str = json_str.replace('```', '').strip()
            if json_str.startswith('json'):
                json_str = json_str[4:].strip()

            data = json.loads(json_str)
            
            # Extract object information
            objects = {obj["name"]: obj for obj in data["objects"]}
            from_obj_name = data["move"]["from"]
            to_obj_name = data["move"]["to"]
            
            to_obj = objects[to_obj_name]

            # Apply resolution scaling if auto-detection is enabled
            if self.auto_detect_scaling:
                # Detect scaling from the current captcha image
                detected_scale = self.detect_resolution_scaling("captcha_temp.png")
                if detected_scale != 1.0:
                    self.resolution_scale_factor = detected_scale

            # Apply coordinate scaling to target object
            scaled_target_coords = self.apply_coordinate_scaling(to_obj["coordinates"], self.resolution_scale_factor)

            # Get distance analysis from AI
            distance_analysis = data.get("distance_analysis", {})
            pixel_distance = distance_analysis.get("pixel_distance", 100)
            # Apply scaling to distance as well
            scaled_pixel_distance = pixel_distance * self.resolution_scale_factor
            drag_strategy = distance_analysis.get("drag_strategy", "TWO_STEP_DRAG")

            print(f"🎯 ADAPTIVE DRAG STRATEGY: {drag_strategy}")
            print(f"📏 Original distance: {pixel_distance:.1f}px, Scaled: {scaled_pixel_distance:.1f}px")
            print(f"🔧 Resolution scale factor: {self.resolution_scale_factor:.3f}")

            # Calculate absolute coordinates
            captcha_left = self.captcha_bbox[0]
            start_x = self.slide_button_absolute[0]
            start_y = self.slide_button_absolute[1]

            # Calculate target position with scaling applied
            target_x = captcha_left + scaled_target_coords["x"]
            end_x = target_x  # Direct alignment, no overshoot
            end_y = start_y   # Maintain horizontal drag
            
            print(f"🎯 Target coordinates: ({end_x}, {end_y})")
            print(f"📍 Actual drag distance: {abs(end_x - start_x)} pixels")

            # Execute drag based on strategy using scaled distance
            if drag_strategy == "SINGLE_DRAG" or scaled_pixel_distance <= self.close_object_threshold:
                return self.execute_single_continuous_drag(start_x, start_y, end_x, end_y, from_obj_name, to_obj_name)
            else:
                return self.execute_two_step_drag(start_x, start_y, end_x, end_y, from_obj_name, to_obj_name, scaled_pixel_distance)
                
        except json.JSONDecodeError as e:
            print(f"❌ JSON parsing error: {e}")
            print(f"❌ Failed to parse: {json_str if 'json_str' in locals() else 'No json_str available'}")
            return False
        except KeyError as e:
            print(f"❌ Missing key in response: {e}")
            print(f"❌ Available data: {data if 'data' in locals() else 'No data available'}")
            return False
        except Exception as e:
            print(f"❌ Movement execution error: {e}")
            print(f"❌ Analysis result: {analysis_result}")
            return False

    def execute_single_continuous_drag(self, start_x, start_y, end_x, end_y, source_obj, target_obj):
        """OPTIMIZED single continuous drag for close objects - ACCURACY PRIORITY with human-like movement"""
        try:
            print("🚀 SINGLE CONTINUOUS DRAG - Human-like precision mode")

            # Calculate drag distance for duration estimation
            drag_distance = abs(end_x - start_x)
            drag_duration = max(2.0, min(4.0, drag_distance / 30))  # Slightly faster with human movement

            print(f"🎯 Human-like dragging {drag_distance}px in {drag_duration:.1f}s")

            # Execute human-like drag movement
            self.human_like_drag(start_x, start_y, end_x, end_y, drag_duration)
            self.add_human_pause(0.3, 0.7)  # Human-like pause after drag

            # Verify complete overlap
            verification_image = self.capture_screen_area("verification_single.png")
            overlap_result = self.verify_complete_overlap(verification_image, source_obj, target_obj)

            if overlap_result and overlap_result.get("complete_overlap_achieved"):
                print("✅ SINGLE DRAG SUCCESS: Complete overlap achieved")
                return True
            else:
                print("⚠️ Single drag incomplete - may need adjustment")
                return False

        except Exception as e:
            print(f"❌ Single drag error: {e}")
            return False

    def execute_two_step_drag(self, start_x, start_y, end_x, end_y, source_obj, target_obj, pixel_distance):
        """OPTIMIZED two-step drag for far objects - COMPLETE OVERLAP FOCUS with human-like movement"""
        try:
            print("🎯 TWO-STEP DRAG - Human-like complete overlap priority")

            # Step 1: Move 60% of the way with human-like movement
            total_distance = abs(end_x - start_x)
            step1_distance = total_distance * 0.6
            step1_x = start_x + (step1_distance if end_x > start_x else -step1_distance)

            print(f"📍 Step 1: Human-like movement {step1_distance:.0f}px to x={step1_x:.0f}")

            # Execute first step with human-like movement
            self.human_like_drag(start_x, start_y, step1_x, end_y, duration_base=1.2)
            self.add_human_pause(0.2, 0.4)  # Human-like pause between steps

            # Check overlap after step 1
            step1_image = self.capture_screen_area("step1_check.png")
            overlap_check = self.verify_complete_overlap(step1_image, source_obj, target_obj)

            if overlap_check and overlap_check.get("complete_overlap_achieved"):
                print("✅ STEP 1 SUCCESS: Complete overlap achieved early")
                return True

            # Step 2: Complete the remaining distance with human-like precision
            remaining_distance = abs(end_x - step1_x)
            print(f"📍 Step 2: Human-like movement for remaining {remaining_distance:.0f}px to target")

            # Execute second step with human-like movement
            self.human_like_drag(step1_x, end_y, end_x, end_y, duration_base=1.5)
            self.add_human_pause(0.3, 0.6)  # Human-like pause after completion

            # Final verification
            final_image = self.capture_screen_area("final_check.png")
            final_overlap = self.verify_complete_overlap(final_image, source_obj, target_obj)

            if final_overlap and final_overlap.get("complete_overlap_achieved"):
                print("✅ TWO-STEP SUCCESS: Complete overlap achieved")
                return True
            else:
                print("⚠️ Two-step drag incomplete - complete overlap not achieved")
                return False

        except Exception as e:
            print(f"❌ Two-step drag error: {e}")
            return False



    def solve_captcha(self):
        """OPTIMIZED main captcha solving method - PERFORMANCE & ACCURACY PRIORITY"""
        try:
            print("🚀 STARTING OPTIMIZED CAPTCHA SOLVER")

            # Capture captcha with minimal processing
            image_path = self.capture_screen_area()
            print("📸 Screenshot captured")

            # Enhanced vision analysis
            analysis = self.analyze_with_vision(image_path)

            if analysis is None:
                print("❌ Failed to get vision analysis")
                return False

            print("🧠 Vision analysis completed")

            # Execute optimized movement
            success = self.execute_movement(analysis)

            if success:
                print("✅ CAPTCHA SOLVED SUCCESSFULLY")
            else:
                print("❌ CAPTCHA SOLVING FAILED")

            return success

        except Exception as e:
            print(f"❌ Captcha solving error: {e}")
            return False




