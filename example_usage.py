#!/usr/bin/env python3
"""
Example usage of the ai.py module as an import

This demonstrates how to use the AutomatedDistanceTool as an imported module
in your own scripts instead of running it as a standalone command-line tool.
"""

# Import the ai module and its convenience functions
from ai import (
    AutomatedDistanceTool,
    create_distance_tool,
    quick_measurement,
    get_object_coordinates,
    get_captcha_measurements
)

def example_basic_usage():
    """Example 1: Basic usage with default settings"""
    print("=" * 60)
    print("🔧 Example 1: Basic Usage")
    print("=" * 60)
    
    # Create a tool instance with default settings
    tool = AutomatedDistanceTool()
    
    # Run a single measurement
    distance = tool.run_automated_measurement("fox and robot")
    
    if distance:
        print(f"✅ Measurement successful: {distance:.2f} pixels")
    else:
        print("❌ Measurement failed")


def example_custom_coordinates():
    """Example 2: Using custom captcha region and slide button coordinates"""
    print("=" * 60)
    print("🔧 Example 2: Custom Coordinates")
    print("=" * 60)
    
    # Define custom coordinates for your specific captcha
    custom_captcha_bbox = (1401, 490, 1731, 751)  # (left, top, right, bottom)
    custom_slide_button = (1442, 714)  # (x, y)
    
    # Create tool with custom coordinates
    tool = create_distance_tool(
        captcha_bbox=custom_captcha_bbox,
        slide_button_coords=custom_slide_button
    )
    
    # Run measurement
    distance = tool.run_automated_measurement("two main objects")
    
    if distance:
        print(f"✅ Custom measurement successful: {distance:.2f} pixels")
        
        # Get detailed report
        report = tool.create_measurement_report(distance)
        print(f"📊 Report timestamp: {report['timestamp']}")
        print(f"📍 Objects detected: {report['objects_detected']}")


def example_quick_measurement():
    """Example 3: Using the quick measurement convenience function"""
    print("=" * 60)
    print("🔧 Example 3: Quick Measurement Function")
    print("=" * 60)
    
    # One-liner measurement with custom settings
    distance = quick_measurement(
        objects="fox and robot",
        captcha_bbox=(1401, 490, 1731, 751),
        slide_button_coords=(1442, 714)
    )
    
    if distance:
        print(f"✅ Quick measurement: {distance:.2f} pixels")
    else:
        print("❌ Quick measurement failed")


def example_get_coordinates_only():
    """Example 4: Get object coordinates without performing drag"""
    print("=" * 60)
    print("🔧 Example 4: Get Coordinates Only")
    print("=" * 60)
    
    # Just get the object coordinates without dragging
    coordinates = get_object_coordinates(
        objects="fox and robot",
        captcha_bbox=(1401, 490, 1731, 751)
    )
    
    if coordinates:
        print("✅ Object coordinates detected:")
        for name, data in coordinates.items():
            print(f"   📍 {name}: {data['coordinates']} - {data['description']}")
    else:
        print("❌ Could not detect object coordinates")


def example_captcha_solver_integration():
    """Example 5: Integration with captcha solver"""
    print("=" * 60)
    print("🔧 Example 5: Captcha Solver Integration")
    print("=" * 60)
    
    # Get comprehensive measurements for captcha solving
    measurements = get_captcha_measurements(
        objects="fox and robot objects",
        captcha_bbox=(1401, 490, 1731, 751),
        slide_button_coords=(1442, 714)
    )
    
    if measurements:
        print("✅ Captcha solver measurements:")
        print(f"   📏 Distance between objects: {measurements['distance_pixels']} pixels")
        print(f"   🎚️  Slide button: {measurements['slide_button']}")
        print(f"   🖱️  Required drag: {measurements['drag_info']['distance']} pixels {measurements['drag_info']['direction']}")
        print(f"   📊 Object 1: {measurements['object1']['name']} at {measurements['object1']['absolute_coords']}")
        print(f"   📊 Object 2: {measurements['object2']['name']} at {measurements['object2']['absolute_coords']}")
    else:
        print("❌ Could not get captcha solver measurements")


def example_advanced_usage():
    """Example 6: Advanced usage with custom processing"""
    print("=" * 60)
    print("🔧 Example 6: Advanced Usage")
    print("=" * 60)
    
    # Create tool instance
    tool = AutomatedDistanceTool()
    
    # Customize the captcha region for your specific use case
    tool.captcha_bbox = (1401, 490, 1731, 751)
    tool.slide_button_absolute = (1442, 714)
    
    # Step-by-step process with custom handling
    print("📸 Step 1: Capturing screenshot...")
    image_path = tool.capture_screenshot()
    
    print("🤖 Step 2: Detecting objects...")
    response = tool.detect_objects_with_grok(image_path, "fox and robot")
    
    if response and tool.parse_detection_response(response):
        print("✅ Objects detected successfully")
        
        # Validate detection before proceeding
        if tool.validate_detection_accuracy():
            print("✅ Detection validation passed")
            
            # Get measurements without dragging
            integration_data = tool.integrate_with_captcha_solver()
            
            if integration_data:
                print("📊 Integration data available:")
                print(f"   🎯 Drag distance needed: {integration_data['drag_info']['distance']} pixels")
                print(f"   📍 Drag direction: {integration_data['drag_info']['direction']}")
                
                # You can now use this data in your own captcha solving logic
                # instead of letting the tool perform the actual drag
                print("🔧 Ready for custom captcha solving implementation")
            else:
                print("❌ Could not get integration data")
        else:
            print("⚠️  Detection validation failed")
    else:
        print("❌ Object detection failed")


def main():
    """Run all examples"""
    print("🚀 AI.py Module Usage Examples")
    print("This demonstrates how to use ai.py as an imported module")
    
    try:
        # Run examples (comment out any you don't want to run)
        example_basic_usage()
        example_custom_coordinates()
        example_quick_measurement()
        example_get_coordinates_only()
        example_captcha_solver_integration()
        example_advanced_usage()
        
        print("\n" + "=" * 60)
        print("🎉 All examples completed!")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n🛑 Examples interrupted by user")
    except Exception as e:
        print(f"\n❌ Error running examples: {e}")


if __name__ == "__main__":
    main()
